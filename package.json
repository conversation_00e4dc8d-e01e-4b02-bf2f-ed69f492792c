{"name": "sakai-vue", "version": "4.3.0", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --fix . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx --fix --ignore-path .gitignore"}, "dependencies": {"@primeuix/themes": "^1.0.0", "@primevue/forms": "^4.3.5", "@vueuse/core": "^13.3.0", "axios": "^1.9.0", "chart.js": "3.3.2", "pinia": "^3.0.3", "primeicons": "^7.0.0", "primevue": "^4.3.1", "tailwindcss-primeui": "^0.5.0", "vue": "^3.4.34", "vue-router": "^4.4.0", "vue-tsc": "^2.2.10", "yup": "^1.6.1", "i18next": "^23.11.2", "i18next-browser-languagedetector": "^7.2.1", "i18next-http-backend": "^2.5.1", "i18next-vue": "^4.0.0"}, "devDependencies": {"@primevue/auto-import-resolver": "^4.3.1", "@rushstack/eslint-patch": "^1.8.0", "@types/mercadopago": "^1.5.11", "@types/node": "^22.15.30", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "autoprefixer": "^10.4.19", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "postcss": "^8.4.40", "prettier": "^3.2.5", "sass": "^1.55.0", "tailwindcss": "^3.4.6", "typescript": "^5.4.5", "unplugin-vue-components": "^0.27.3", "vite": "^5.3.1"}}