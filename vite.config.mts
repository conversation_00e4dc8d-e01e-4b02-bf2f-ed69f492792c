import { fileURLToPath, URL } from 'node:url';

import { PrimeVueResolver } from '@primevue/auto-import-resolver';
import vue from '@vitejs/plugin-vue';
import Components from 'unplugin-vue-components/vite';
import { defineConfig, loadEnv } from 'vite';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
    // Load .env
    const env = loadEnv(mode, process.cwd(), '')
    process.env = { ...process.env, ...env }

    return {
        optimizeDeps: {
            noDiscovery: true,
            include: ['yup']
        },
        plugins: [
            vue(),
            Components({
                resolvers: [PrimeVueResolver()]
            })
        ],
        resolve: {
            alias: {
                '@': fileURLToPath(new URL('./src', import.meta.url))
            }
        },
        server: {
            port: 3000,
            proxy: {
                // https://vitejs.dev/config/server-options.html
            },
        },
        define: {
            // vitejs does not support process.env so we have to redefine it
            'process.env': process.env,
        }
    }
});

