<script setup>
import { ref } from 'vue';

import AppMenuItem from './AppMenuItem.vue';

const model = ref([
    {
        label: 'Home',
        items: [{ label: 'Dashboard', icon: 'pi pi-fw pi-home', to: '/' }]
    },
    {
        label: 'Administración',
        items: [
            {
                label: 'Clientes',
                icon: 'pi pi-fw pi-users',
                to: '/customers'
            },
            {
                label: 'Empleados',
                icon: 'pi pi-fw pi-users',
                to: '/employees'
            }
        ]
    },
    {
        label: 'Configuración',
        items: [
            {
                label: 'Ajustes',
                icon: 'pi pi-fw pi-cog',
                to: '/settings'
            },
            {
                label: 'Usuarios',
                icon: 'pi pi-fw pi-users',
                to: '/users'
            }
        ]
    }
]);
</script>

<template>
    <ul class="layout-menu">
        <template v-for="(item, i) in model" :key="item">
            <app-menu-item v-if="!item.separator" :item="item" :index="i"></app-menu-item>
            <li v-if="item.separator" class="menu-separator"></li>
        </template>
    </ul>
</template>

<style lang="scss" scoped></style>
