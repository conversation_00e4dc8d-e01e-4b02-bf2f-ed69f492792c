import { TypeWithKey } from '@/models/TypeWithKey.ts';

export const mapErrorCode = (errorCode: any) => {
    if (!errorCode) {
        errorCode = 'UNKNOWN';
    }
    const codeMatcher: TypeWithKey<string> = {
        BAD_CREDENTIALS: 'Usuario no encontrado, revisá los datos y volvé a intentar',
        INVALID_ARGUMENTS: 'Datos invalidos, revisá el formulario y corregí los campos con error',
        INTERNAL_SERVER_ERROR: 'Hubo un error inesperado, intentá de nuevo o comunicate con soporte si el inconveniente persiste',
        UNKNOWN: 'Hubo un error inesperado, intentá de nuevo o comunicate con soporte si el inconveniente persiste',
        ERR_NETWORK: 'Error de conexión con el servidor',
        USER_ALREADY_EXISTS: 'El nombre de usario no esta disponible',
        ACCESS_DENIED: 'Permisos insuficientes',
        AUTHENTICATION_ERROR: 'Usuario no identificado', // no access token provided
        JWT_EXPIRED: 'Sesión expirada'
    };

    const mappedError = codeMatcher[errorCode];
    return mappedError != null ? mappedError : errorCode;
};

export const isMissingAuthOrExpiredJwtError = (errorCode: any): boolean => {
    if (!errorCode) {
        return false;
    }

    const codeMatcher: TypeWithKey<string> = {
        AUTHENTICATION_ERROR: 'Usuario no identificado',
        JWT_EXPIRED: 'Sesión expirada'
    };

    const mappedError = codeMatcher[errorCode];
    return mappedError != null ? true : false;
};
