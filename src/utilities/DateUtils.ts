/**
 * Formatea una fecha a "dd/MM/yyyy"
 * @param dateInput  Date objeto o string parseable por Date()
 * @param options    Intl.DateTimeFormatOptions (por defecto dd/MM/yyyy)
 */
export function formatDate(
    dateInput: Date | string,
    options: Intl.DateTimeFormatOptions = {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
    }
): string {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
    return date.toLocaleDateString('es-AR', options);
}

/**
 * Formatea una fecha y hora a "dd/MM/yyyy HH:mm:ss"
 * @param dateInput  Date objeto o string parseable por Date()
 * @param options    Intl.DateTimeFormatOptions (por defecto dd/MM/yyyy HH:mm:ss)
 */
export function formatDateTime(
    dateInput: Date | string,
    options: Intl.DateTimeFormatOptions = {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    }
): string {
    const date = typeof dateInput === 'string' ? new Date(dateInput) : dateInput;
    return date.toLocaleString('es-AR', options);
}
