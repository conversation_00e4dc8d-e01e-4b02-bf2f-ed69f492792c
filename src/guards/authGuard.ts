import { useAuthStore } from '@/stores/auth.ts';
import type { NavigationGuard } from 'vue-router';
import { ApiResponse } from '@/services/types.ts';
import { UserInfoResponse } from '@/services/auth/types.ts';
import { isMissingAuthOrExpiredJwtError } from '@/utilities/ErrorCodeMapper.ts';
import { useToast } from 'primevue/usetoast';

/**
 * Performs authentication and authorization checks based on the destination route requirements:
 * {@link RouteMeta#requiresAuth}, {@link RouteMeta#requiredPrivileges}  / {@link RouteMeta#requiresGuest}
 * @param to
 * @param from
 * @param next
 */
export const authGuard: NavigationGuard = async (to, from, next) => {
    const authStore = useAuthStore();
    const toast = useToast();

    if (to.matched.some((record) => record.meta.requiresAuth)) {
        // Route requires logged in user
        if (authStore.isLoggedIn()) {
            // Get fresh user info to make sure that the privileges checked by FE are always up-to-date
            const userInfo: ApiResponse<UserInfoResponse> = await authStore.dispatchGetUserInfo();
            if (userInfo.success) {
                // Check authenticated user privileges
                if (to.meta.requiredPrivileges == null || authStore.hasPrivileges(to.meta.requiredPrivileges)) {
                    next();
                } else {
                    toast.add({
                        severity: 'warn',
                        summary: 'Permisos insuficientes',
                        detail: 'No es posible continuar la navegación',
                        life: 5000
                    });
                    next(false);
                }
            } else if (isMissingAuthOrExpiredJwtError(userInfo.errorResponse?.codeName)) {
                next('/auth/login');
            } else {
                toast.add({
                    severity: 'warn',
                    summary: 'Error inesperado',
                    detail: 'Vuelve a intentarlo mas tarde',
                    life: 5000
                });
                next(false);
            }
        } else {
            next('/auth/login');
        }
    } else if (to.matched.some((record) => record.meta.requiresGuest)) {
        // Route requires guest user
        if (!authStore.isLoggedIn()) {
            next();
        } else {
            next('/');
        }
    } else {
        // Route does not explicitly require being logged in or not
        next();
    }
};
