export type Subscription = {
    id: string;
    code: string;
    status: string;
    createDate: Date;
    invoices: SubscriptionInvoice[];
    nextDueDate?: Date;
    trialExpirationDate: Date;
};

export type SubscriptionInvoice = {
    id: string;
    status: string;
    issueDate: Date;
    dueDate: Date;
    items: SubscriptionInvoiceItem[];
    invoiceTotal: number;
};

export type SubscriptionInvoiceItem = {
    itemName: string;
    from: Date;
    to: Date;
    quantity: number;
    price: number;
};
