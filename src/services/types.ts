export type ApiResponse<T> = {
    success: boolean;
    content?: T;
    status?: number;
    errorResponse?: ApiErrorResponse;
    errorCode?: string;
};

export type ApiErrorResponse = {
    codeName?: string;
    message?: string;
    retryable?: boolean;
};

export type PagedResource<R> = {
    content: R[];
    pageSize: number;
    pageNumber: number;
    pageElements: number;
    totalElements: number;
    totalPages: number;
};
