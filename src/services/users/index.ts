import http from '../api';
import { CreateUserInput, GetUsersInput, PrivilegeOption, UpdateUserInput, User } from './types';
import { PagedResource } from '@/services/types.ts';

async function getUsers(input: GetUsersInput) {
    const params = new URLSearchParams();
    if (input.pageSize) {
        params.append('pageSize', input.pageSize.toString());
    }
    if (input.pageNumber) {
        params.append('pageNumber', input.pageNumber.toString());
    }
    if (input.usernameFilter) {
        params.append('filter.username', input.usernameFilter.toString());
    }
    if (input.sortBy) {
        params.append('sortBy', input.sortBy);
    }
    if (input.sortDirection) {
        params.append('sortDirection', input.sortDirection.toUpperCase());
    }
    return await http.get<PagedResource<User>>('users/', { params });
}

async function getUser(id: string) {
    return await http.get<User>(`users/${id}`);
}

async function deleteUser(id: string) {
    return await http.delete(`users/${id}`);
}

async function createUser(input: CreateUserInput, profilePicture?: File) {
    const formData = new FormData();
    // Wrap the JSON in a Blob so it is sent as a multipart/form-data field:
    formData.append('user', new Blob([JSON.stringify(input)], { type: 'application/json' }));

    if (profilePicture) {
        formData.append('file', profilePicture);
    }

    return await http.post<User>('users/', formData, {
        headers: {
            'Content-Type': 'multipart/form-data'
        }
    });
}

async function updateUser(input: UpdateUserInput) {
    return await http.patch<User>(`users/${input.id}`, input);
}

async function getPrivilegeOptions(userId?: string) {
    const params = new URLSearchParams();
    if (userId) {
        params.append('userId', userId);
    }
    return await http.get<PrivilegeOption[]>('meta/privilege-options', { params });
}

export default {
    getUsers,
    getUser,
    createUser,
    updateUser,
    deleteUser,
    getPrivilegeOptions
};
