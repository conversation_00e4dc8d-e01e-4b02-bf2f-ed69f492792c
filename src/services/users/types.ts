export type User = {
    id: string;
    username: string;
    role: string;
    profilePictureUrl?: string;
    privileges: string[];
    grantedResources: UserGrantedResource[];
    createDate: Date;
};

export type UserGrantedResource = {
    type: string;
    key: string;
};

export type CreateUserInput = {
    username: string;
    password: string;
    privileges: string[];
};

export type UpdateUserInput = {
    id: string;
    password?: string;
    privileges?: string[];
    grantedResources?: UserGrantedResource[];
};

export type GetUsersInput = {
    pageSize?: number;
    pageNumber?: number;
    usernameFilter?: string;
    sortBy?: string;
    sortDirection?: string;
};

export type PrivilegeOption = {
    group: string;
    name: string;
    disabled?: boolean;
    selected?: boolean;
};
