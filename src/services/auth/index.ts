import http from '../api';
import { LoginInput, LoginResponse, RegisterUserInput, RegisterUserResponse, UserInfoResponse } from './types';

async function register(input: RegisterUserInput) {
    return await http.post<RegisterUserResponse>('/onboarding/subscription', input);
}

async function login(input: LoginInput) {
    return await http.post<LoginResponse>('auth/login', input);
}

async function userInfo() {
    return await http.get<UserInfoResponse>('auth/userInfo');
}

async function logout() {
    return await http.post<LoginResponse>('auth/logout');
}

export default {
    register,
    login,
    userInfo,
    logout
};
