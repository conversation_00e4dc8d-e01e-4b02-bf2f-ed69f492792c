import axios from 'axios';
import { useAuthStore } from '@/stores/auth.ts';
import router from '@/router';

const instance = axios.create({
    baseURL: process.env.VUE_APP_API_URL,
    withCredentials: true,
    responseType: 'json',
    headers: {
        Accept: 'application/json',
        'Content-type': 'application/json'
    }
});

// Send Authorization header when token is available
instance.interceptors.request.use((config) => {
    const authStore = useAuthStore();
    const token = authStore.accessToken;
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});

// Response interceptor to redirect to login page when session expired error is received
instance.interceptors.response.use(
    (response) => response,
    (error) => {
        const authStore = useAuthStore();
        if (error.response.data.codeName == 'JWT_EXPIRED') {
            // TODO: attempt to refresh token, else logout
            authStore.dispatchLogout().then(() => router.push('/auth/login'));
        } else {
            throw error;
        }
    }
);

export default instance;
