import { User } from '@/services/users/types.ts';

export type Employee = {
    id: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    email: string;
    createDate: Date;
    user?: User;
};

export type CreateEmployeeInput = {
    firstName: string;
    lastName: string;
    phoneNumber: string;
    systemAccess: boolean;
    email?: string;
    password?: string;
    privileges?: string[];
};

export type UpdateEmployeeInput = {
    id: string;
    firstName: string;
    lastName: string;
    phoneNumber: string;
    email?: string;
};

export type GetEmployeesInput = {
    pageSize?: number;
    pageNumber?: number;
    firstnameFilter?: string;
    sortBy?: string;
    sortDirection?: string;
};
