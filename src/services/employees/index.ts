import http from '../api';
import { CreateEmployeeInput, Employee, GetEmployeesInput, UpdateEmployeeInput } from './types';
import { PagedResource } from '@/services/types.ts';

async function getEmployees(input: GetEmployeesInput) {
    const params = new URLSearchParams();
    if (input.pageSize) {
        params.append('pageSize', input.pageSize.toString());
    }
    if (input.pageNumber) {
        params.append('pageNumber', input.pageNumber.toString());
    }
    if (input.firstnameFilter) {
        params.append('filter.firstName', input.firstnameFilter.toString());
    }
    if (input.sortBy) {
        params.append('sortBy', input.sortBy);
    }
    if (input.sortDirection) {
        params.append('sortDirection', input.sortDirection.toUpperCase());
    }
    return await http.get<PagedResource<Employee>>('employees/', { params });
}

async function getEmployee(id: string) {
    return await http.get<Employee>(`employees/${id}`);
}

async function deleteEmployee(id: string) {
    return await http.delete(`employees/${id}`);
}

async function createEmployee(input: CreateEmployeeInput) {
    return await http.post<Employee>('employees/', input);
}

async function updateEmployee(input: UpdateEmployeeInput) {
    return await http.put<Employee>(`employees/${input.id}`, input);
}

export default {
    getEmployees,
    getEmployee,
    createEmployee,
    updateEmployee,
    deleteEmployee
};
