import { defineStore } from 'pinia';
import { ref } from 'vue';
import { CreateEmployeeInput, Employee, GetEmployeesInput, UpdateEmployeeInput } from '../services/employees/types';
import { ApiErrorResponse, ApiResponse, PagedResource } from '../services/types';
import { API } from '../services';
import { AxiosError } from 'axios';

export const useEmployeesStore = defineStore('employeesStore', () => {
    const employees = ref<Employee[]>([]);
    const totalElements = ref<number>(0);
    const totalPages = ref<number>(0);

    function initEmployees(data: PagedResource<Employee>) {
        employees.value = data.content;
        totalElements.value = data.totalElements;
        totalPages.value = data.totalPages;
    }

    function addNewEmployee(employee: Employee) {
        employees.value.push(employee);
    }

    function updateEmployee(employee: Employee) {
        const idx = employees.value.findIndex((e) => e.id === employee.id);
        if (idx === -1) return;
        employees.value.splice(idx, 1, employee);
    }

    async function dispatchGetEmployees(input: GetEmployeesInput): Promise<ApiResponse<PagedResource<Employee>>> {
        try {
            const { status, data } = await API.employees.getEmployees(input);
            initEmployees(data);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchGetEmployee(employeeId: string): Promise<ApiResponse<Employee>> {
        try {
            const { status, data } = await API.employees.getEmployee(employeeId);
            updateEmployee(data);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchCreateEmployee(input: CreateEmployeeInput): Promise<ApiResponse<Employee>> {
        try {
            const { status, data } = await API.employees.createEmployee(input);
            addNewEmployee(data);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchUpdateEmployee(input: UpdateEmployeeInput): Promise<ApiResponse<Employee>> {
        try {
            const { status, data } = await API.employees.updateEmployee(input);
            updateEmployee(data);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchDeleteEmployee(id: string): Promise<ApiResponse<void>> {
        try {
            const { status } = await API.employees.deleteEmployee(id);
            const idx = employees.value.findIndex((e) => e.id === id);
            if (idx !== -1) {
                employees.value.splice(idx, 1);
            }
            return {
                success: true,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    return {
        employees,
        totalElements,
        totalPages,
        dispatchGetEmployees,
        dispatchGetEmployee,
        dispatchCreateEmployee,
        dispatchUpdateEmployee,
        dispatchDeleteEmployee
    };
});
