import { defineStore } from 'pinia';
import { RemovableRef, useLocalStorage } from '@vueuse/core';
import { LoginInput, LoginResponse, RegisterUserInput, RegisterUserResponse, UserInfoResponse } from '../services/auth/types';
import { User } from '../services/users/types';
import { ApiErrorResponse, ApiResponse } from '../services/types';
import { API } from '../services';
import { AxiosError } from 'axios';

export const useAuthStore = defineStore('authStore', () => {
    const user: RemovableRef<User | null> = useLocalStorage('user', {} as User);
    const accessToken: RemovableRef<String | null> = useLocalStorage('accessToken', null);

    function initUser(data: User) {
        user.value = data;
    }

    function initAccessToken(data: string) {
        accessToken.value = data;
    }

    function reset() {
        user.value = null;
        accessToken.value = null;
    }

    function isLoggedIn() {
        return accessToken.value != null;
    }

    function hasPrivilege(privilege: string) {
        if (!user.value) {
            return false;
        }
        return user.value?.privileges.indexOf(privilege) > -1;
    }

    function hasPrivileges(privileges: string[]) {
        return privileges.every(hasPrivilege);
    }

    function hasAnyPrivileges(privileges: string[]) {
        return privileges.some(hasPrivilege);
    }

    async function dispatchLogin(input: LoginInput): Promise<ApiResponse<LoginResponse>> {
        try {
            const { status, data } = await API.auth.login(input);
            initUser(data.user);
            initAccessToken(data.accessToken);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchGetUserInfo(): Promise<ApiResponse<UserInfoResponse>> {
        try {
            const { status, data } = await API.auth.userInfo();
            initUser(data.user);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchLogout() {
        await API.auth.logout();
        localStorage.clear();
        reset();
    }

    async function dispatchRegister(input: RegisterUserInput): Promise<ApiResponse<RegisterUserResponse>> {
        try {
            const { status, data } = await API.auth.register(input);
            initUser(data.user);
            initAccessToken(data.accessToken);
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    return {
        user,
        accessToken,
        isLoggedIn,
        hasPrivilege,
        hasPrivileges,
        hasAnyPrivileges,
        dispatchLogin,
        dispatchGetUserInfo,
        dispatchLogout,
        dispatchRegister
    };
});
