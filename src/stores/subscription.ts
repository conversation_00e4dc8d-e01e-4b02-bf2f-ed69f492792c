import { defineStore } from 'pinia';
import { Subscription } from '../services/subscription/types';
import { ApiErrorResponse, ApiResponse } from '../services/types';
import { API } from '../services';
import { AxiosError } from 'axios';
import { RemovableRef, useLocalStorage } from '@vueuse/core';

export const useSubscriptionStore = defineStore('subscriptionStore', () => {
    const subscription: RemovableRef<Subscription | null> = useLocalStorage('subscription', {} as Subscription);

    async function dispatchGetSubscription(): Promise<ApiResponse<Subscription>> {
        try {
            const { status, data } = await API.subscription.getSubscription();
            subscription.value = data;

            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    async function dispatchActivateSubscription(): Promise<ApiResponse<Subscription>> {
        try {
            const { status, data } = await API.subscription.activateSubscription(subscription.value?.id);
            await dispatchGetSubscription();
            return {
                success: true,
                content: data,
                status: status
            };
        } catch (error) {
            const _error = error as AxiosError<ApiErrorResponse>;
            return {
                success: false,
                status: _error.response?.status,
                errorResponse: _error.response?.data
            };
        }
    }

    function isActive() {
        return subscription.value && (subscription.value?.status == 'ACTIVE' || subscription.value?.status == 'TRIAL');
    }

    function isTrial() {
        return subscription.value && (subscription.value?.status == 'TRIAL' || subscription.value?.status == 'TRIAL_ENDED');
    }

    function isTrialEnded() {
        return subscription.value && subscription.value?.status == 'TRIAL_ENDED';
    }

    return {
        subscription,
        isActive,
        isTrial,
        dispatchGetSubscription,
        dispatchActivateSubscription,
        isTrialEnded
    };
});
