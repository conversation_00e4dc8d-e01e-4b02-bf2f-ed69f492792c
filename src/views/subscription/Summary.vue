<script setup lang="ts">
import { useTranslation } from 'i18next-vue'
import { useSubscriptionStore } from '@/stores/subscription.ts';
import { formatDate } from "../../utilities/DateUtils.ts";
import { formatCurrency } from "../../utilities/MoneyUtils.ts";
import { ref } from "vue";
import { SubscriptionInvoice } from "@/services/subscription/types.ts";
import PaymentModal from "@/views/subscription/PaymentModal.vue";

const { t } = useTranslation()
const subscriptionStore = useSubscriptionStore();

const showModal = ref(false)
const selectedInvoice = ref<SubscriptionInvoice | null>(null)
const modalRef = ref<InstanceType<typeof PaymentModal>>()

function iniciarPago() {
  modalRef.value?.open(1000, 'factura-abc-123')
}

function handlePagoExitoso(result: any) {
  console.log('Pago OK:', result)
}

function handleErrorPago(err: any) {
  console.error('Pago fallido:', err)
}

function openModal(invoice: SubscriptionInvoice) {
  selectedInvoice.value = invoice
  showModal.value = true
}

function closeModal() {
  showModal.value = false
  selectedInvoice.value = null
}

function getInvoiceStatusTagSeverity(status: string, invoiceDueDate: string) {
  let now = new Date();
  if (status === 'PAYMENT_COMPLETE') {
    return 'success'
  } else if (status === 'PAYMENT_PENDING' && new Date(invoiceDueDate) < now) {
    return 'danger';
  }
  return 'info';
}
</script>

<template>
  <!-- Subscription Summary Card -->
  <div class="grid grid-cols-12 gap-6 mb-6">
    <div class="col-span-12">
      <div class="card">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div class="p-4">
            <div class="font-semibold text-surface-900 dark:text-surface-0">{{ t('plan_name') }}</div>
            <div class="text-lg mt-2">{{ subscriptionStore.subscription.code }}</div>
          </div>
          <div class="p-4">
            <div class="font-semibold text-surface-900 dark:text-surface-0">{{ t('plan_status') }}</div>
            <div class="text-lg mt-2">{{ subscriptionStore.subscription.status }}</div>
          </div>
          <div class="p-4">
            <div class="font-semibold text-surface-900 dark:text-surface-0">{{ t('plan_start_date') }}</div>
            <div class="text-lg mt-2">{{ formatDate(subscriptionStore.subscription.createDate) }}</div>
          </div>
          <div class="p-4">
            <div class="font-semibold text-surface-900 dark:text-surface-0" v-if="subscriptionStore.isTrial()">
              {{ t('plan_trial_expiration_date') }}
            </div>
            <div class="font-semibold text-surface-900 dark:text-surface-0" v-else>
              {{ t('plan_next_due_date') }}
            </div>
            <div class="text-lg mt-2" v-if="subscriptionStore.isTrial()">
              {{ formatDate(subscriptionStore.subscription.trialExpirationDate) }}
            </div>
            <div class="text-lg mt-2" v-else>
              {{ formatDate(subscriptionStore.subscription.nextDueDate) }}
            </div>
          </div>
        </div>

        <!-- Trial Ended Message -->
        <Message v-if="subscriptionStore.isTrialEnded()" severity="success" class="mt-4">
          <div class="flex flex-col gap-4">
            <p class="m-0">
              Tu periodo de prueba finalizo, pero no te preocupes, podes seguir usando el sistema activando tu suscripción
            </p>
            <Button
              @click="subscriptionStore.dispatchActivateSubscription"
              severity="success"
              size="large"
              label="Activar suscripción"
            />
          </div>
        </Message>
      </div>
    </div>
  </div>

  <!-- Invoices Table -->
  <div class="grid grid-cols-12 gap-6 mb-6">
    <div class="col-span-12">
      <div class="card">
        <div class="font-semibold text-xl mb-6">{{ t('subscription_invoices') }}</div>

        <DataTable
          :value="subscriptionStore.subscription.invoices"
          stripedRows
          @row-click="openModal($event.data)"
          :rowHover="true"
          class="cursor-pointer"
        >
          <Column :header="t('invoice_issue_date')" field="issueDate">
            <template #body="slotProps">
              {{ formatDate(slotProps.data.issueDate) }}
            </template>
          </Column>
          <Column :header="t('invoice_due_date')" field="dueDate">
            <template #body="slotProps">
              {{ formatDate(slotProps.data.dueDate) }}
            </template>
          </Column>
          <Column :header="t('invoice_total')" field="invoiceTotal">
            <template #body="slotProps">
              {{ formatCurrency(slotProps.data.invoiceTotal) }}
            </template>
          </Column>
          <Column :header="t('invoice_status')" field="status">
            <template #body="slotProps">
              <Tag
                :value="t(slotProps.data.status)"
                :severity="getInvoiceStatusTagSeverity(slotProps.data.status, slotProps.data.dueDate)"
              />
            </template>
          </Column>

          <template #empty>
            <div class="text-center py-4">
              {{ t('subscription_no_pending_invoices') }}
            </div>
          </template>
        </DataTable>
      </div>
    </div>
  </div>

  <!-- Invoice Details Dialog -->
  <Dialog
    v-model:visible="showModal"
    :header="t('invoice_details')"
    :modal="true"
    :style="{ width: '50rem' }"
    @hide="closeModal"
  >
    <div v-if="selectedInvoice">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <div>
          <div class="flex items-center gap-2 mb-3">
            <span class="font-semibold">{{ t('invoice_status') }}:</span>
            <Tag
              :value="t(selectedInvoice.status)"
              :severity="getInvoiceStatusTagSeverity(selectedInvoice.status, selectedInvoice.dueDate)"
            />
          </div>
          <div class="mb-3">
            <span class="font-semibold">{{ t('invoice_total') }}:</span>
            {{ formatCurrency(selectedInvoice.invoiceTotal) }}
          </div>
        </div>
        <div>
          <div class="mb-3">
            <span class="font-semibold">{{ t('invoice_issue_date') }}:</span>
            {{ formatDate(selectedInvoice.issueDate) }}
          </div>
          <div class="mb-3">
            <span class="font-semibold">{{ t('invoice_due_date') }}:</span>
            {{ formatDate(selectedInvoice.dueDate) }}
          </div>
        </div>
      </div>

      <DataTable :value="selectedInvoice.items" size="small">
        <Column :header="t('item_name')" field="itemName" />
        <Column :header="t('item_from')" field="from" class="text-right">
          <template #body="slotProps">
            {{ formatDate(slotProps.data.from) }}
          </template>
        </Column>
        <Column :header="t('item_to')" field="to" class="text-right">
          <template #body="slotProps">
            {{ formatDate(slotProps.data.to) }}
          </template>
        </Column>
        <Column :header="t('item_total')" field="price" class="text-right">
          <template #body="slotProps">
            {{ formatCurrency(slotProps.data.price) }}
          </template>
        </Column>
      </DataTable>
    </div>

    <template #footer>
      <Button
        :label="t('close')"
        severity="secondary"
        @click="closeModal"
      />
    </template>
  </Dialog>

  <!-- Payment Section -->
  <div class="grid grid-cols-12 gap-6">
    <div class="col-span-12">
      <div class="card">
        <Button
          label="Pagar ahora"
          severity="success"
          @click="iniciarPago"
        />

        <PaymentModal
          ref="modalRef"
          @success="handlePagoExitoso"
          @error="handleErrorPago"
        />
      </div>
    </div>
  </div>
</template>
