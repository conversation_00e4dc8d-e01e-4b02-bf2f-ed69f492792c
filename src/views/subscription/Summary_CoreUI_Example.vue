<script setup lang="ts">
import {useTranslation} from 'i18next-vue'
import {useSubscriptionStore} from '@/stores/subscription.ts';
import {CContainer, CRow} from "@coreui/vue-pro/dist/esm/components/grid";
import {formatDate} from "../../utilities/DateUtils.ts";
import {formatCurrency} from "../../utilities/MoneyUtils.ts";
import {ref} from "vue";
import {SubscriptionInvoice} from "@/services/subscription/types.ts";
import PaymentModal from "@/views/subscription/PaymentModal.vue";

const {t} = useTranslation()
const subscriptionStore = useSubscriptionStore();

const showModal = ref(false)
const selectedInvoice = ref<SubscriptionInvoice | null>(null)
const modalRef = ref<InstanceType<typeof PaymentModal>>()

function iniciarPago() {
  modalRef.value?.open(1000, 'factura-abc-123')
}

function handlePagoExitoso(result: any) {
  console.log('Pago OK:', result)
}

function handleErrorPago(err: any) {
  console.error('Pago fallido:', err)
}

function openModal(invoice: SubscriptionInvoice) {
  selectedInvoice.value = invoice
  showModal.value = true
}

function closeModal() {
  showModal.value = false
  selectedInvoice.value = null
}

function getInvoiceStatusBadgeColor(status :string, invoiceDueDate :string) {
  let now = new Date();
  if (status === 'PAYMENT_COMPLETE') {
    return 'success'
  } else if (status === 'PAYMENT_PENDING' && new Date(invoiceDueDate) < now) {
    return 'danger';
  }
  return 'primary';
}

</script>

<template>
  <CRow>
    <CCol :xs="12">
      <CCard class="mb-4">
        <CCardBody>
          <CContainer fluid>
            <CRow :xs="{ gutter: 2 }">
              <CCol :xs="{ span: 6 }">
                <div class="p-3"><b>{{ t('plan_name') }}</b> {{ subscriptionStore.subscription.code }}</div>
              </CCol>
              <CCol :xs="{ span: 6 }">
                <div class="p-3"><b>{{ t('plan_status') }}</b> {{ subscriptionStore.subscription.status }}</div>
              </CCol>
            </CRow>
            <CRow :xs="{ gutter: 2 }">
              <CCol :xs="{ span: 6 }">
                <div class="p-3"><b>{{ t('plan_start_date') }}</b> {{ formatDate(subscriptionStore.subscription.createDate) }}</div>
              </CCol>
              <CCol :xs="{ span: 6 }">
                <div class="p-3" v-if="subscriptionStore.isTrial()"><b>{{ t('plan_trial_expiration_date') }}</b>
                  {{ formatDate(subscriptionStore.subscription.trialExpirationDate) }}
                </div>
                <div class="p-3" v-else><b>{{ t('plan_next_due_date') }}</b>
                  {{ formatDate(subscriptionStore.subscription.nextDueDate) }}
                </div>
              </CCol>
            </CRow>
          </CContainer>
          <CCallout v-if="subscriptionStore.isTrialEnded()" color="success">
            Tu periodo de prueba finalizo, pero no te preocupes, podes seguir usando el sistema activando tu suscripción
            <div class="clearfix" style="margin:30px"></div>
            <CButton @click="subscriptionStore.dispatchActivateSubscription" color="success" size="lg">Activar suscripción</CButton>
          </CCallout>
        </CCardBody>
      </CCard>
    </CCol>
  </CRow>

  <CRow>
    <CCol :xs="12">
      <CCard class="mb-4">
        <CCardHeader>
          <strong>{{ t('subscription_invoices') }}</strong>
        </CCardHeader>
        <CCardBody>

          <CTable striped hover bordered>
            <CTableHead>
              <CTableRow>
                <CTableHeaderCell scope="col">{{ t('invoice_issue_date') }}</CTableHeaderCell>
                <CTableHeaderCell scope="col">{{ t('invoice_due_date') }}</CTableHeaderCell>
                <CTableHeaderCell scope="col">{{ t('invoice_total') }}</CTableHeaderCell>
                <CTableHeaderCell scope="col">{{ t('invoice_status') }}</CTableHeaderCell>
              </CTableRow>
            </CTableHead>
            <CTableBody>
              <CTableRow
                  v-for="invoice in subscriptionStore.subscription.invoices"
                  :key="invoice.id"
                  @click="openModal(invoice)"
                  style="cursor: pointer;"
              >
                <CTableDataCell>{{ formatDate(invoice.issueDate) }}</CTableDataCell>
                <CTableDataCell>{{ formatDate(invoice.dueDate) }}</CTableDataCell>
                <CTableDataCell>{{ formatCurrency(invoice.invoiceTotal) }}</CTableDataCell>
                <CTableDataCell>
                  <CBadge :color="getInvoiceStatusBadgeColor(invoice.status, invoice.dueDate)">
                    {{ t(invoice.status) }}
                  </CBadge>
                </CTableDataCell>
              </CTableRow>
              <CTableRow v-if="!subscriptionStore.subscription.invoices.length">
                <CTableDataCell colspan="4"><p class="text-center">{{ t('subscription_no_pending_invoices') }}</p></CTableDataCell>
              </CTableRow>
            </CTableBody>
          </CTable>
        </CCardBody>
      </CCard>
    </CCol>
  </CRow>


  <CModal v-model:visible="showModal" size="lg" @close="closeModal">
    <CModalHeader closeButton>
      <strong>{{ t('invoice_details') }}</strong>
    </CModalHeader>
    <CModalBody>
      <div v-if="selectedInvoice">
        <div class="row">
          <!-- Columna izquierda -->
          <div class="col-sm-6">
            <p><strong>{{ t('invoice_status') }}:</strong>
              <CBadge :color="getInvoiceStatusBadgeColor(selectedInvoice.status, selectedInvoice.dueDate)">
                {{ t(selectedInvoice.status) }}
              </CBadge>
            </p>
            <p><strong>{{ t('invoice_total') }}:</strong> {{ formatCurrency(selectedInvoice.invoiceTotal) }}</p>
          </div>
          <!-- Columna derecha, texto alineado a la derecha -->
          <div class="col-sm-6">
            <p><strong>{{ t('invoice_issue_date') }}:</strong> {{ formatDate(selectedInvoice.issueDate) }}</p>
            <p><strong>{{ t('invoice_due_date') }}:</strong> {{ formatDate(selectedInvoice.dueDate) }}</p>
          </div>
        </div>

        <CTable small>
          <CTableHead>
            <CTableRow>
              <CTableHeaderCell>{{ t('item_name') }}</CTableHeaderCell>
              <CTableHeaderCell class="text-end">{{ t('item_from') }}</CTableHeaderCell>
              <CTableHeaderCell class="text-end">{{ t('item_to') }}</CTableHeaderCell>
              <CTableHeaderCell class="text-end">{{ t('item_total') }}</CTableHeaderCell>
            </CTableRow>
          </CTableHead>
          <CTableBody>
            <CTableRow v-for="item in selectedInvoice.items" :key="item.id">
              <CTableDataCell>{{ item.itemName }}</CTableDataCell>
              <CTableDataCell class="text-end">{{ formatDate(item.from) }}</CTableDataCell>
              <CTableDataCell class="text-end">{{ formatDate(item.to) }}</CTableDataCell>
              <CTableDataCell class="text-end">{{ formatCurrency(item.price) }}</CTableDataCell>
            </CTableRow>
          </CTableBody>
        </CTable>
      </div>
    </CModalBody>
    <CModalFooter>
      <CButton color="secondary" @click="closeModal">
        {{ t('close') }}
      </CButton>
    </CModalFooter>
  </CModal>

  <CContainer>
    <CButton color="success" @click="iniciarPago">Pagar ahora</CButton>

    <PaymentModal
        ref="modalRef"
        @success="handlePagoExitoso"
        @error="handleErrorPago"
    />
  </CContainer>
</template>
