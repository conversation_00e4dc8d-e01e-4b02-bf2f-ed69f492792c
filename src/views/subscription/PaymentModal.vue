<template>
  <CModal size="xl" backdrop="static" :visible="visible" @close="handleClose" @show="onModalShown">
    <CModalHeader>
      <h5 class="modal-title">Activar suscripción</h5>
      <CButton close @click="handleClose" />
    </CModalHeader>

    <CModalBody>
      <div class="row">
        <div class="col-md-6">
          <div class="row">
            <div class="col-md-6 pt-lg-5">
              <CCard class="text-center">
                <CCardHeader><h4 class="my-0">Plan 1</h4></CCardHeader>
                <CCardBody>
                  <h2 class="card-title">$10.000 <small class="text-muted" style="font-size: 0.5em;">/ mes</small></h2>
                  <ul class="list-unstyled mt-3 mb-4">
                    <li>Imagenes ilimitadas</li>
                    <li>Usuarios ilimitados</li>
                    <li>Soporte prioritario</li>
                  </ul>
                  <CButton color="primary" variant="outline">Seleccionar</CButton>
                </CCardBody>
              </CCard>
            </div>
            <div class="col-md-6 pt-lg-5">
              <CCard class="text-center shadow">
                <CCardHeader><h4 class="my-0">Plan 2</h4></CCardHeader>
                <CCardBody>
                  <h2 class="card-title">$20.000 <small class="text-muted" style="font-size: 0.5em;">/ mes</small></h2>
                  <ul class="list-unstyled mt-3 mb-4">
                    <li>Imagenes ilimitadas</li>
                    <li>Usuarios ilimitados</li>
                    <li>Soporte prioritario</li>
                  </ul>
                  <CIcon icon="cilCheckCircle" size="4xl" class="me-2 text-success"/>
                </CCardBody>
              </CCard>
            </div>
          </div>
        </div>
        <div class="col-md-6">
          <div class="cards-title-container mb-3" style="position: relative;">
            <h1 class="cards-title">Tarjeta de crédito o débito</h1>
            <div class="cards-container">
              <div role="img" data-testid="icon" alt="Tarjeta ingresada visa" class="card-icon-container" aria-hidden="true"><svg viewBox="0 0 25 25" aria-hidden="true" fill="none" height="25" width="25" xmlns="http://www.w3.org/2000/svg"><path d="m12.408 9.692-1.28 5.987H9.579l1.28-5.987zm6.516 3.866.816-2.249.469 2.249zm1.728 2.12h1.433l-1.25-5.986h-1.323a.705.705 0 0 0-.659.44l-2.323 5.547h1.625l.324-.894h1.986zm-4.042-1.954c.006-1.58-2.185-1.667-2.17-2.373.004-.215.21-.443.657-.502a2.93 2.93 0 0 1 1.527.268l.272-1.269a4.2 4.2 0 0 0-1.449-.265c-1.53 0-2.608.814-2.617 1.979-.01.861.769 1.342 1.356 1.629.603.293.806.481.803.744-.004.401-.481.579-.926.585-.779.013-1.23-.21-1.591-.377l-.28 1.312c.361.166 1.028.31 1.722.318 1.626 0 2.69-.805 2.696-2.049m-6.414-4.032-2.51 5.987H6.049L4.814 10.9c-.075-.294-.14-.402-.368-.526-.372-.202-.987-.391-1.528-.509l.037-.174H5.59c.336 0 .638.223.714.61l.652 3.465 1.612-4.075z" fill="#1434CB"></path></svg></div>
              <div role="img" data-testid="icon" alt="Tarjeta ingresada master" class="card-icon-container" aria-hidden="true"><svg viewBox="0 0 21 21" aria-hidden="true" fill="none" height="21" width="21" xmlns="http://www.w3.org/2000/svg"><path clip-rule="evenodd" d="M20.085 10.657c0 3.307-2.654 5.99-5.927 5.99-3.274 0-5.928-2.682-5.928-5.99s2.654-5.99 5.928-5.99 5.927 2.682 5.927 5.99" fill-rule="evenodd" fill="#F79E1B"></path><path clip-rule="evenodd" d="M12.772 10.657c0 3.307-2.653 5.99-5.927 5.99S.918 13.964.918 10.656s2.654-5.99 5.927-5.99c3.274 0 5.927 2.682 5.927 5.99" fill-rule="evenodd" fill="#EB001B"></path><path clip-rule="evenodd" d="M10.501 5.941a6 6 0 0 0-2.27 4.715c0 1.914.887 3.62 2.27 4.716a6 6 0 0 0 2.27-4.716 6 6 0 0 0-2.27-4.715" fill-rule="evenodd" fill="#FF5F00"></path></svg></div>
              <div role="img" data-testid="icon" alt="Tarjeta ingresada amex" class="card-icon-container" aria-hidden="true"><svg viewBox="0 0 25 25" aria-hidden="true" fill="none" height="25" width="25" xmlns="http://www.w3.org/2000/svg"><path clip-rule="evenodd" d="M2.918 7.839A4.92 4.92 0 0 1 7.84 2.917h9.322a4.92 4.92 0 0 1 4.922 4.922v9.322a4.92 4.92 0 0 1-4.922 4.922H7.84a4.92 4.92 0 0 1-4.922-4.922z" fill-rule="evenodd" fill="#016FD0"></path><path clip-rule="evenodd" d="M9.617 19.135V12.13l12.468.01v1.936l-1.442 1.54 1.442 1.555v1.975h-2.3l-1.224-1.349-1.214 1.355z" fill-rule="evenodd" fill="#fff"></path><path clip-rule="evenodd" d="M10.45 18.368V12.9h4.635v1.26H11.95v.854h3.06v1.238h-3.06v.84h3.135v1.275z" fill-rule="evenodd" fill="#016FD0"></path><path clip-rule="evenodd" d="m15.063 18.368 2.565-2.737-2.565-2.73h1.985l1.567 1.733 1.572-1.733h1.9v.044l-2.51 2.686 2.51 2.659v.078h-1.92l-1.594-1.75-1.58 1.75z" fill-rule="evenodd" fill="#016FD0"></path><path clip-rule="evenodd" d="M10.196 5.912h3.005l1.056 2.397V5.912h3.71l.64 1.797.642-1.797h2.834v7.006H7.108z" fill-rule="evenodd" fill="#fff"></path><path clip-rule="evenodd" d="M10.765 6.673 8.34 12.135h1.664l.457-1.094h2.48l.457 1.094h1.705l-2.415-5.462zm.209 3.142.727-1.739.727 1.739z" fill-rule="evenodd" fill="#016FD0"></path><path clip-rule="evenodd" d="M15.082 12.134V6.672l2.338.009 1.204 3.357 1.21-3.366h2.251v5.462l-1.448.013V8.394l-1.367 3.74h-1.322l-1.395-3.751v3.75z" fill-rule="evenodd" fill="#016FD0"></path></svg></div>
              <div role="img" data-testid="icon" alt="Tarjeta ingresada naranja" class="card-icon-container" aria-hidden="true"><svg viewBox="0 0 25 25" aria-hidden="true" fill="none" height="25" width="25" xmlns="http://www.w3.org/2000/svg"><path d="m18.489 12.156-3.175-4.894a.38.38 0 0 0-.324-.175h-2.115a.18.18 0 0 0-.************* 0 0 0 .007.182l3.31 5.101a.03.03 0 0 1 0 .035l-3.31 5.086a.18.18 0 0 0-.************* 0 0 0 .158.093h2.117a.38.38 0 0 0 .323-.175l3.174-4.878a.6.6 0 0 0 0-.651" fill="#FE5000"></path><path d="M19.145 13.152a.18.18 0 0 0-.302 0l-.872 1.34a.86.86 0 0 0 0 .937l1.486 2.277a.34.34 0 0 0 .285.153h2.191c.056 0 .108-.03.134-.079a.15.15 0 0 0-.006-.154zm-.302-1.358a.18.18 0 0 0 .302 0L22.06 7.32a.15.15 0 0 0 .006-.155.15.15 0 0 0-.134-.08H19.74a.34.34 0 0 0-.284.155L17.97 9.516a.86.86 0 0 0 0 .938z" fill="#50007F"></path><path d="M11.835 7.083H9.98c-.1 0-.18.08-.18.178v6.541c-.002.108-.058.14-.12.036L5.4 7.245a.36.36 0 0 0-.3-.162h-2a.18.18 0 0 0-.182.18v10.42c0 .105.086.181.182.181h1.853c.1 0 .18-.08.18-.178v-6.541c.003-.108.059-.14.121-.035l4.28 6.592a.36.36 0 0 0 .3.162h2a.18.18 0 0 0 .182-.18V7.264a.18.18 0 0 0-.181-.181" fill="#FE5000"></path></svg></div>
              <div role="img" data-testid="icon" alt="Tarjeta ingresada cabal" class="card-icon-container" aria-hidden="true"><svg viewBox="0 0 25 25" aria-hidden="true" fill="none" height="25" width="25" xmlns="http://www.w3.org/2000/svg"><path d="M2.944 22.084c-.007-.105-.018-.211-.018-.316q0-9.26-.008-18.513c0-.291.078-.338.33-.338 17.537.007.963.01 ************ 0 .215.012.324.02q0 6.422-.007 12.845c0 .982.063 1.967-.036 2.948-.193 1.882-1.682 3.312-3.462 3.325h-.462c-13.262 0-11.998.014-15.161.022" fill="#fff"></path><path clip-rule="evenodd" d="m2.944 22.084-.006-.09a3 3 0 0 1-.012-.226q0-9.26-.008-18.513c0-.291.078-.338.33-.338 8.527.004 8.99.006 9.23.007h9.27q.114.002.23.012l.094.008q0 6.422-.007 12.845.001.411.01.823c.012.709.025 1.418-.046 2.125-.193 1.882-1.682 3.312-3.462 3.325h-.462c-9.894 0-11.703.008-13.297.015h-.001c-.543.002-1.06.005-1.863.007M13.106 6.257v3.09c0 .***************.176l.033.032q1.164 1.233 2.333 2.464c.31.328.317.615.009.94a752 752 0 0 1-2.303 ********** 0 0 0-.169.427c.008.391.005.783.003 1.175l-.003.589v1.123l.045-.018c.018-.007.028-.01.035-.018l5.308-5.546c.36-.379.351-.844-.007-1.225q-.6-.634-1.206-1.264l-.44-.459zM6.79 13.163c-.399-.412-.428-.882-.059-1.268 1.418-1.487 2.839-2.97 4.284-4.477l1.11-1.159v.798c0 .775 0 1.523.003 2.274.001.113-.058.174-.12.236l-.024.025q-1.16 1.205-2.32 2.435c-.312.338-.312.61 0 .94q1.143 1.206 2.3 2.412a.56.56 0 0 1 .162.43v2.92c-.2-.21-.382-.395-.557-.575l-.187-.191z" fill-rule="evenodd" fill="#016398"></path></svg></div>
            </div>
          </div>

          <form id="form-checkout">

            <div class="row">
              <div class="col-md-12 mb-3">
                <label for="form-checkout__cardNumber" class="form-label"><small>Número de tarjeta</small></label>
                <input type="text" placeholder="1234 1234 1234 1234" id="form-checkout__cardNumber" :class="['form-control', { 'is-invalid': fieldErrors.cardNumber }]" />
                <small v-if="fieldErrors.cardNumber" class="text-danger">{{ fieldErrors.cardNumber }}</small>
              </div>
            </div>
            <div class="row">
              <div class="col-md-6 mb-3">
                <label for="form-checkout__expirationDate" class="form-label"><small>Vencimiento</small></label>
                <input type="text" placeholder="MM/AA" id="form-checkout__expirationDate" :class="['form-control', { 'is-invalid': fieldErrors.expirationDate }]" />
                <small v-if="fieldErrors.expirationDate" class="text-danger">{{ fieldErrors.expirationDate }}</small>
              </div>
              <div class="col-md-6 mb-3">
                <label for="form-checkout__securityCode" class="form-label"><small>CVV</small></label>
                <input type="text" placeholder="Ej.: 123" id="form-checkout__securityCode" :class="['form-control', { 'is-invalid': fieldErrors.securityCode }]" />
                <small v-if="fieldErrors.securityCode" class="text-danger">{{ fieldErrors.securityCode }}</small>
              </div>
            </div>

            <div class="mb-3">
              <label for="form-checkout__cardholderName" class="form-label"><small>Nombre del titular</small></label>
              <input type="text" placeholder="María López" id="form-checkout__cardholderName" :class="['form-control', { 'is-invalid': fieldErrors.cardholderName }]" />
              <small v-if="fieldErrors.cardholderName" class="text-danger">{{ fieldErrors.cardholderName }}</small>
            </div>

            <div class="row">
              <div class="col-md-3 mb-3">
                <label for="form-checkout__identificationType" class="form-label"><small>Tipo</small></label>
                <select id="form-checkout__identificationType" :class="['form-select', { 'is-invalid': fieldErrors.identificationType }]" />
                <small v-if="fieldErrors.identificationType" class="text-danger">{{ fieldErrors.identificationType }}</small>
              </div>

              <div class="col-md-8 mb-3">
                <label for="form-checkout__identificationNumber" class="form-label"><small>Número de documento</small></label>
                <input type="text" placeholder="99999999" id="form-checkout__identificationNumber" :class="['form-control', { 'is-invalid': fieldErrors.identificationNumber }]" />
                <small v-if="fieldErrors.identificationNumber" class="text-danger">{{ fieldErrors.identificationNumber }}</small>
              </div>
            </div>

            <div class="mb-3 d-none">
              <label for="form-checkout__issuer" class="form-label"><small>Emisor</small></label>
              <select id="form-checkout__issuer" :class="['form-select', { 'is-invalid': fieldErrors.issuer }]" />
              <small v-if="fieldErrors.issuer" class="text-danger">{{ fieldErrors.issuer }}</small>
            </div>

            <div class="mb-3">
              <label for="form-checkout__cardholderEmail" class="form-label"><small>Email</small></label>
              <input type="email" placeholder="<EMAIL>" id="form-checkout__cardholderEmail" :class="['form-control', { 'is-invalid': fieldErrors.cardholderEmail }]" />
              <small v-if="fieldErrors.cardholderEmail" class="text-danger">{{ fieldErrors.cardholderEmail }}</small>
            </div>

            <div class="mb-3 d-none">
              <label for="form-checkout__installments" class="form-label"><small>Cuotas</small></label>
              <select id="form-checkout__installments" :class="['form-select', { 'is-invalid': fieldErrors.installments }]" />
              <small v-if="fieldErrors.installments" class="text-danger">{{ fieldErrors.installments }}</small>
            </div>

            <div class="d-grid mt-3 d-md-block">
              <button
                  display="block"
                  type="submit"
                  class="btn btn-lg btn-primary mt-3 mb-3"
                  :disabled="isSubmitting"
              >
            <span
                v-if="isSubmitting"
                class="spinner-border spinner-border-sm me-2"
                role="status"
                aria-hidden="true"
            ></span>
                {{ isSubmitting ? 'Procesando...' : 'Pagar' }}
              </button>
            </div>
          </form>
        </div>
      </div>
    </CModalBody>
  </CModal>
</template>

<script setup lang="ts">
import {
  CModal,
  CModalHeader,
  CModalBody,
  CButton
} from '@coreui/vue-pro'

import { ref, reactive } from 'vue'
import { loadMercadoPago } from '@mercadopago/sdk-js'
import CIcon from '@coreui/icons-vue'

interface MPFieldError {
  message: string
  code: string
}

const emit = defineEmits<{
  success: [result: any]
  error: [err: any]
}>()

const publicKey = process.env.MP_PUBLIC_KEY || ''
const visible = ref(false)
const isSubmitting = ref(false)
const fieldErrors = reactive<Record<string, string>>({})

const errorCodeToField: Record<string, string> = {
  '205': 'cardNumber',
  '208': 'expirationDate',
  '209': 'expirationDate',
  '212': 'identificationType',
  '213': 'identificationNumber',
  '214': 'identificationNumber',
  '220': 'cardIssuerId',
  '221': 'cardholderName',
  '224': 'securityCode',
  'E203': 'securityCode',
  'E301': 'cardNumber',
  '316': 'cardholderName',
  '322': 'identificationType',
  '323': 'identificationNumber',
  '324': 'identificationNumber',
  '325': 'expirationDate',
  'default': 'generic'
}

let amount = 0
let invoiceId = ''
let cardForm: any = null

function handleClose() {
  visible.value = false
  if (cardForm && typeof cardForm.unmount === 'function') {
    cardForm.unmount()
    cardForm = null
  }
  Object.keys(fieldErrors).forEach(key => delete fieldErrors[key])
}

async function handleSubmit() {
  Object.keys(fieldErrors).forEach(key => delete fieldErrors[key])

  if (!cardForm || typeof cardForm.getCardFormData !== 'function') {
    emit('error', new Error('Formulario de tarjeta no inicializado'))
    return
  }

  const data = cardForm.getCardFormData()

  if (!data.token) {
    emit('error', new Error('Los datos ingresados son inválidos.'))
    return
  }

  isSubmitting.value = true

  try {
    const response = await fetch('/api/payment/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        token: data.token,
        cardholderEmail: data.cardholderEmail,
        invoiceId
      })
    })
    const result = await response.json()
    emit('success', result)
    visible.value = false
  } catch (err) {
    emit('error', err)
  } finally {
    isSubmitting.value = false
  }
}

async function mountCardForm() {
  await loadMercadoPago()
  const mp = new window.MercadoPago(publicKey, { locale: 'es-AR' })

  cardForm = mp.cardForm({
    amount: String(amount),
    autoMount: true,
    form: {
      id: 'form-checkout',
      cardholderName: { id: 'form-checkout__cardholderName' },
      cardholderEmail: { id: 'form-checkout__cardholderEmail' },
      cardNumber: { id: 'form-checkout__cardNumber' },
      expirationDate: { id: 'form-checkout__expirationDate' },
      securityCode: { id: 'form-checkout__securityCode' },
      installments: { id: 'form-checkout__installments' },
      identificationType: { id: 'form-checkout__identificationType' },
      identificationNumber: { id: 'form-checkout__identificationNumber' },
      issuer: { id: 'form-checkout__issuer' }
    },
    callbacks: {
      onFormMounted: (error: any) => {
        if (error) {
          console.error('Error al montar el formulario:', error)
        } else {
          console.log('Formulario de MP montado correctamente')
        }
      },
      onValidityChange: (errors?: MPFieldError[], field?: string) => {
        if (!field) return
        if (!errors || errors.length === 0) {
          delete fieldErrors[field]
        } else {
          fieldErrors[field] = errors.map(e => `(${e.code}) ${e.message}`).join('\n')
        }
      },
      onSubmit: async (event: Event) => {
        event.preventDefault()
        await handleSubmit()
      },
      onCardTokenReceived: (errors?: MPFieldError[]) => {
        Object.keys(fieldErrors).forEach(k => delete fieldErrors[k])

        if (errors && errors.length > 0) {
          for (const err of errors) {
            const field = errorCodeToField[err.code]
            if (field) {
              fieldErrors[field] = `(${err.code}) ${err.message}`
            }
          }
        }
      }
    }
  })
}

function onModalShown() {
  if (cardForm && typeof cardForm.unmount === 'function') {
    cardForm.unmount()
    cardForm = null
  }
  mountCardForm()
}

function open(pAmount: number, pInvoiceId: string) {
  amount = pAmount
  invoiceId = pInvoiceId
  visible.value = true
}

defineExpose({ open })
</script>

<style scoped>
.cards-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.card-icon-container {
  justify-content: center;
  display: flex;
  align-items: center;
  border: 1px solid #DFE0E4;
  margin: 0 4px;
  overflow: hidden;
  background: white;
  height: 24px;
  width: 36px;
  border-radius: 4px;
  max-width: 30px;
  max-height: 22px;
}
.cards-title {
  font-size: 20px;
  font-weight: 600;
  color: '#1A1F27';
  margin: 0;
  max-width: 100%;
  word-break: break-word;
}
.cards-title-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
</style>
