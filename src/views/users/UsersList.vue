<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useUsersStore } from '@/stores/users';
import { useAuthStore } from '@/stores/auth';
import { storeToRefs } from 'pinia';
import { GetUsersInput, User, PrivilegeOption } from '@/services/users/types';
import { formatDate } from '@/utilities/DateUtils';

import { useToast } from 'primevue/usetoast';
import { useTranslation } from 'i18next-vue';
import UserEdit from './UserEdit.vue';

const { t } = useTranslation();

// Stores
const usersStore = useUsersStore();
const authStore = useAuthStore();
const { users, totalElements } = storeToRefs(usersStore);
const toast = useToast();

// Refs
const dt = ref();
const loading = ref(false);

// Dialog state
const userDetailsVisible = ref(false);
const selectedUser = ref<User | null>(null);
const userEditVisible = ref(false);
const userToEdit = ref<User | null>(null);

// Privilege options for grouping
const privilegeOptions = ref<PrivilegeOption[]>([]);

// Pagination
const first = ref(0);
const rows = ref(10);
const currentPage = computed(() => Math.floor(first.value / rows.value) + 1);

// Filtering
const usernameFilter = ref<string | null>(null);

// Sorting
const sortField = ref<string | undefined>(undefined);
const sortOrder = ref<number | undefined>(undefined);

// Computed properties
const privilegesByGroup = computed(() => {
    const groups: { [key: string]: PrivilegeOption[] } = {};
    privilegeOptions.value.forEach((option) => {
        if (!groups[option.group]) {
            groups[option.group] = [];
        }
        groups[option.group].push(option);
    });
    return groups;
});

// Load users on component mount
onMounted(async () => {
    await loadUsers();
});

// Load users function
async function loadUsers() {
    loading.value = true;

    const input: GetUsersInput = {
        pageSize: rows.value,
        pageNumber: currentPage.value,
        usernameFilter: usernameFilter.value || undefined,
        sortBy: sortField.value,
        sortDirection: sortOrder.value === 1 ? 'ASC' : sortOrder.value === -1 ? 'DESC' : undefined
    };

    await usersStore.dispatchGetUsers(input);
    loading.value = false;
}

// Event handlers
function onPage(event: any) {
    first.value = event.first;
    rows.value = event.rows;
    loadUsers();
}

function onSort(event: any) {
    sortField.value = event.sortField;
    sortOrder.value = event.sortOrder;
    loadUsers();
}

function onFilter() {
    first.value = 0; // Reset to first page when filtering
    loadUsers();
}

// Expose refresh method for parent component
function refreshList() {
    loadUsers();
}

// Expose the refresh method
defineExpose({
    refreshList
});

function getRoleLabelColor(role: string): string {
    switch (role) {
        case 'ADMIN':
            return 'danger';
        case 'OWNER':
            return 'info';
        case 'EMPLOYEE':
            return 'success';
        default:
            return 'secondary';
    }
}

function editUser(user: User) {
    userToEdit.value = { ...user }; // Create a copy to avoid mutating the original
    userEditVisible.value = true;
    // Close the user details dialog when opening edit modal
    userDetailsVisible.value = false;
    selectedUser.value = null;
}

async function onRowClick(event: any) {
    selectedUser.value = event.data;
    await loadPrivilegeOptions();
    userDetailsVisible.value = true;
}

function hideUserDetails() {
    userDetailsVisible.value = false;
    selectedUser.value = null;
}

function hideUserEdit() {
    userEditVisible.value = false;
    userToEdit.value = null;
}

function onUserUpdated() {
    hideUserEdit();
    loadUsers(); // Refresh the list
    toast.add({
        severity: 'success',
        summary: t('success'),
        detail: t('userUpdatedSuccessfully'),
        life: 3000
    });
}

// Load privilege options for grouping
async function loadPrivilegeOptions() {
    if (!selectedUser.value) return;

    const response = await usersStore.dispatchGetPrivilegeOptions(selectedUser.value.id);
    if (response.success && response.content) {
        privilegeOptions.value = response.content;
    }
}

// Check if user has a specific privilege
function userHasPrivilege(privilegeName: string): boolean {
    return selectedUser.value?.privileges.includes(privilegeName) || false;
}

function isNotOwner(user: User): boolean {
    return user.role !== 'OWNER';
}

// Delete user function
async function deleteUser(user: User) {
    const response = await usersStore.dispatchDeleteUser(user.id);

    if (response.success) {
        toast.add({
            severity: 'success',
            summary: t('success'),
            detail: t('userDeletedSuccessfully'),
            life: 3000
        });
        // Refresh the list to reflect the changes
        loadUsers();
    } else {
        toast.add({
            severity: 'error',
            summary: t('error'),
            detail: t('userDeleteError'),
            life: 3000
        });
    }
}
</script>

<template>
    <div>
        <DataTable
            ref="dt"
            :value="users"
            dataKey="id"
            :paginator="true"
            :rows="rows"
            :first="first"
            :totalRecords="totalElements"
            :loading="loading"
            :lazy="true"
            :sortField="sortField"
            :sortOrder="sortOrder"
            selectionMode="single"
            paginatorTemplate="FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown"
            :rowsPerPageOptions="[5, 10, 25, 50]"
            currentPageReportTemplate="Showing {first} to {last} of {totalRecords} users"
            @page="onPage"
            @sort="onSort"
            @row-click="onRowClick"
            stripedRows
        >
            <template #header>
                <div class="flex flex-wrap gap-2 items-center justify-between">
                    <h4 class="m-0">{{ t('manageUsers') }}</h4>
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText v-model="usernameFilter" :placeholder="t('searchByUsername')" @input="onFilter" />
                    </IconField>
                </div>
            </template>

            <Column style="width: 5rem" :exportable="false">
                <template #body="slotProps">
                    <Avatar v-if="slotProps.data.profilePictureUrl" :image="slotProps.data.profilePictureUrl" shape="circle" size="normal" />
                    <Avatar v-else :label="slotProps.data.username.charAt(0).toUpperCase()" shape="circle" size="normal" />
                </template>
            </Column>

            <Column field="username" :header="t('email')" sortable style="min-width: 16rem">
                <template #body="slotProps">
                    <span class="font-medium">{{ slotProps.data.username }}</span>
                </template>
            </Column>

            <Column field="createDate" :header="t('createDate')" sortable style="min-width: 12rem">
                <template #body="slotProps">
                    <span>{{ formatDate(slotProps.data.createDate) }}</span>
                </template>
            </Column>

            <Column field="role" :header="t('role')" style="min-width: 10rem">
                <template #body="slotProps">
                    <Tag :value="t(slotProps.data.role)" :severity="getRoleLabelColor(slotProps.data.role)" />
                </template>
            </Column>

            <Column :exportable="false" style="min-width: 8rem">
                <template #body="slotProps">
                    <Button icon="pi pi-pencil" outlined rounded class="mr-2" v-if="authStore.hasPrivilege('USER_UPDATE') && isNotOwner(slotProps.data)" @click="editUser(slotProps.data)" />
                    <Button
                        icon="pi pi-trash"
                        outlined
                        rounded
                        severity="danger"
                        v-if="authStore.hasPrivilege('USER_DELETE') && isNotOwner(slotProps.data)"
                        @click="deleteUser(slotProps.data)"
                    />
                </template>
            </Column>
        </DataTable>

        <!-- User Details Dialog -->
        <Dialog v-model:visible="userDetailsVisible" :header="t('userDetails')" :modal="true" :style="{ width: '50rem' }" @hide="hideUserDetails">
            <div v-if="selectedUser" class="flex flex-col gap-4">
                <!-- User Avatar and Basic Info -->
                <div class="flex items-center gap-4 pb-4 border-b border-surface-200">
                    <Avatar v-if="selectedUser.profilePictureUrl" :image="selectedUser.profilePictureUrl" shape="circle" size="xlarge" />
                    <Avatar v-else :label="selectedUser.username.charAt(0).toUpperCase()" shape="circle" size="xlarge" />
                    <div>
                        <h3 class="m-0 text-xl font-semibold">{{ selectedUser.username }}</h3>
                        <Tag :value="t(selectedUser.role)" :severity="getRoleLabelColor(selectedUser.role)" class="mt-2" />
                    </div>
                </div>

                <!-- User Details -->
                <div>
                    <label class="block text-sm font-medium text-surface-600 mb-1">{{ t('createDate') }}</label>
                    <p class="text-surface-900">{{ formatDate(selectedUser.createDate) }}</p>
                </div>

                <!-- Privileges -->
                <div v-if="Object.keys(privilegesByGroup).length > 0">
                    <label class="block text-sm font-medium text-surface-600 mb-3">{{ t('privileges') }}</label>

                    <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-4 gap-4">
                        <div v-for="(groupOptions, groupName) in privilegesByGroup" :key="groupName" class="border border-surface-200 rounded-lg p-4">
                            <h6 class="text-sm font-semibold text-surface-600 mb-2">{{ t(groupName) }}</h6>
                            <div class="flex flex-col gap-2">
                                <div v-for="option in groupOptions" :key="option.name" class="flex items-center">
                                    <!-- Show checkmark for granted privileges, cross for not granted -->
                                    <template v-if="userHasPrivilege(option.name)">
                                        <i class="pi pi-check text-green-500 mr-2"></i>
                                        <span class="text-sm text-surface-900">{{ t(option.name) }}</span>
                                    </template>
                                    <template v-else>
                                        <i class="pi pi-times text-red-500 mr-2"></i>
                                        <span class="text-sm text-surface-400" :class="{ 'text-surface-400': option.disabled }">{{ t(option.name) }}</span>
                                    </template>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <template #footer>
                <Button :label="t('close')" icon="pi pi-times" @click="hideUserDetails" class="p-button-text" />
                <Button v-if="authStore.hasPrivilege('USER_UPDATE') && isNotOwner(selectedUser)" :label="t('edit')" icon="pi pi-pencil" @click="editUser(selectedUser!)" severity="secondary" />
            </template>
        </Dialog>

        <!-- User Edit Dialog -->
        <Dialog v-model:visible="userEditVisible" :header="t('editUser')" :modal="true" :style="{ width: '50rem' }" @hide="hideUserEdit">
            <UserEdit v-if="userToEdit" :user="userToEdit" @user-updated="onUserUpdated" @cancel="hideUserEdit" />
        </Dialog>
    </div>
</template>
